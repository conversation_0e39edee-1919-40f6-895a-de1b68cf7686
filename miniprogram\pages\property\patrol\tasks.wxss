/* 巡更任务详情页面样式 */
.tasks-container {
  padding: 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: white;
  border-radius: 12rpx;
  padding: 6rpx;
  margin-bottom: 20rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
}

.filter-tab.active {
  background: #1989fa;
  color: white;
  font-weight: 500;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #999;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 任务列表 */
.task-list {
  margin-bottom: 120rpx;
}

.task-item {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}

.task-content {
  padding: 24rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.task-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-overdue {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 任务时间 */
.task-time {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.time-label {
  color: #666;
  margin-right: 8rpx;
}

.time-value {
  color: #333;
  font-weight: 500;
}

/* 任务操作 */
.task-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  min-width: 120rpx;
}

.action-btn.primary {
  background: #1989fa;
  color: white;
}

.action-btn.secondary {
  background: #f7f8fa;
  color: #1989fa;
  border: 1rpx solid #1989fa;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}
