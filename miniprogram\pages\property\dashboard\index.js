// 物业工作台页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    loading: false,
    stats: {
      pendingRepair: 0,
      pendingComplaint: 0
    },
    recentItems: {
      repairs: [],
      complaints: []
    }
  },

  onLoad() {
    console.log('物业工作台页面加载')
  },

  onShow() {
    handlePropertyPageShow(this, this.loadDashboardData)
  },

  // 加载工作台数据
  async loadDashboardData() {
    this.setData({ loading: true })
    try {
      await Promise.all([
        this.loadStats(),
        this.loadRecentItems()
      ])
    } catch (error) {
      console.error('加载工作台数据失败:', error)
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const res = await app.request({
        url: '/api/wx/property/stats',
        method: 'POST'
      })

      if (res.code === 0) {
        this.setData({
          stats: res.data
        })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 加载最近的报修和投诉
  async loadRecentItems() {
    try {
      const [repairRes, complaintRes] = await Promise.all([
        app.request({
          url: '/api/wx/property/repair/list',
          method: 'POST',
          data: { pageNum: 1, pageSize: 3 }
        }),
        app.request({
          url: '/api/wx/property/complaint/list',
          method: 'POST',
          data: { pageNum: 1, pageSize: 3 }
        })
      ])

      this.setData({
        'recentItems.repairs': repairRes.code === 0 ? repairRes.data.list : [],
        'recentItems.complaints': complaintRes.code === 0 ? complaintRes.data.list : []
      })
    } catch (error) {
      console.error('加载最近项目失败:', error)
    }
  },

  // 跳转到工单管理
  goToOrders(e) {
    const type = e.currentTarget.dataset.type || 'repair'
    wx.navigateTo({
      url: `/pages/property/orders/index?activeTab=${type}`
    })
  },

  // 跳转到巡检记录
  goToPatrol() {
    wx.navigateTo({
      url: '/pages/property/patrol/index'
    })
  },

  // 查看报修详情
  viewRepairDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/property/orders/detail?id=${id}&type=repair`
    })
  },

  // 查看投诉详情
  viewComplaintDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/property/orders/detail?id=${id}&type=complaint`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData().then(() => {
      wx.stopPullDownRefresh()
    })
  }
})
